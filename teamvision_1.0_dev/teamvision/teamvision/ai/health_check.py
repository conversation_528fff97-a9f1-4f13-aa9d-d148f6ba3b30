# coding=utf-8
"""
AI服务健康检查模块
"""

import time
from datetime import datetime, timedelta
from typing import Dict, Any, List
from django.utils import timezone
from django.core.cache import cache
from gatesidelib.common.simplelogger import SimpleLogger
from teamvision.ai.ragflow_client_v2 import ragflow_client, TestCaseGenerationRequest
from teamvision.ai.models import AIConfiguration, AIUsageStatistics


class AIHealthChecker:
    """AI服务健康检查器"""
    
    CACHE_KEY_PREFIX = 'ai_health_check'
    CHECK_INTERVAL = 300  # 5分钟检查一次
    
    def __init__(self):
        self.last_check_time = None
        self.health_status = {
            'overall': 'unknown',
            'ragflow': 'unknown',
            'database': 'unknown',
            'cache': 'unknown',
            'last_check': None,
            'details': {}
        }
    
    def check_all(self) -> Dict[str, Any]:
        """执行完整的健康检查"""
        SimpleLogger.info("开始AI服务健康检查")
        start_time = time.time()
        
        try:
            # 检查各个组件
            ragflow_status = self._check_ragflow_service()
            database_status = self._check_database()
            cache_status = self._check_cache()
            
            # 汇总状态
            overall_status = self._calculate_overall_status([
                ragflow_status['status'],
                database_status['status'],
                cache_status['status']
            ])
            
            self.health_status = {
                'overall': overall_status,
                'ragflow': ragflow_status['status'],
                'database': database_status['status'],
                'cache': cache_status['status'],
                'last_check': timezone.now().isoformat(),
                'check_duration': round(time.time() - start_time, 2),
                'details': {
                    'ragflow': ragflow_status,
                    'database': database_status,
                    'cache': cache_status
                }
            }
            
            # 缓存结果
            cache.set(
                f'{self.CACHE_KEY_PREFIX}:status',
                self.health_status,
                timeout=self.CHECK_INTERVAL
            )
            SimpleLogger.info(f"AI服务健康检查完成，总体状态: {overall_status}")
            
        except Exception as e:
            SimpleLogger.exception(f"AI服务健康检查失败: {str(e)}")
            self.health_status['overall'] = 'error'
            self.health_status['details']['error'] = str(e)
        
        return self.health_status
    
    def _check_ragflow_service(self) -> Dict[str, Any]:
        """检查RAGFlow服务状态"""
        try:
            start_time = time.time()
            
            # 创建简单的测试请求
            test_request = TestCaseGenerationRequest(
                requirement_description="健康检查测试",
                module_name="测试模块",
                project_context="健康检查",
                generation_type="functional",
                case_count=1
            )
            
            # 调用RAGFlow服务（设置较短的超时时间）
            response = ragflow_client.generate_test_cases(test_request, timeout=10)
            
            response_time = round(time.time() - start_time, 2)
            
            if response.success:
                return {
                    'status': 'healthy',
                    'response_time': response_time,
                    'message': 'RAGFlow服务正常',
                    'test_cases_count': len(response.test_cases) if response.test_cases else 0
                }
            else:
                return {
                    'status': 'unhealthy',
                    'response_time': response_time,
                    'message': f'RAGFlow服务异常: {response.message}',
                    'error': response.message
                }
                
        except Exception as e:
            SimpleLogger.exception(f"RAGFlow健康检查失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'RAGFlow服务连接失败: {str(e)}',
                'error': str(e)
            }
    
    def _check_database(self) -> Dict[str, Any]:
        """检查数据库连接和AI相关表"""
        try:
            start_time = time.time()
            
            # 测试数据库查询
            from teamvision.ai.models import AIGenerationSession, AIGeneratedTestCase
            
            # 简单查询测试
            session_count = AIGenerationSession.objects.count()
            case_count = AIGeneratedTestCase.objects.count()
            
            # 测试写入（创建一个测试配置）
            test_config, created = AIConfiguration.objects.get_or_create(
                config_type='health_check',
                config_key='test_config',
                defaults={
                    'config_value': {'test': True, 'timestamp': timezone.now().isoformat()},
                    'description': '健康检查测试配置',
                    'is_active': False
                }
            )
            
            if not created:
                # 更新时间戳
                test_config.config_value['timestamp'] = timezone.now().isoformat()
                test_config.save()
            
            response_time = round(time.time() - start_time, 2)
            
            return {
                'status': 'healthy',
                'response_time': response_time,
                'message': '数据库连接正常',
                'session_count': session_count,
                'case_count': case_count
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'数据库连接失败: {str(e)}',
                'error': str(e)
            }
    
    def _check_cache(self) -> Dict[str, Any]:
        """检查缓存服务"""
        try:
            start_time = time.time()
            
            # 测试缓存读写
            test_key = f'{self.CACHE_KEY_PREFIX}:test'
            test_value = {'timestamp': timezone.now().isoformat(), 'test': True}
            
            # 写入测试
            cache.set(test_key, test_value, timeout=60)
            
            # 读取测试
            cached_value = cache.get(test_key)
            
            if cached_value and cached_value.get('test'):
                response_time = round(time.time() - start_time, 2)
                return {
                    'status': 'healthy',
                    'response_time': response_time,
                    'message': '缓存服务正常'
                }
            else:
                return {
                    'status': 'unhealthy',
                    'message': '缓存读写测试失败'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f'缓存服务异常: {str(e)}',
                'error': str(e)
            }
    
    def _calculate_overall_status(self, statuses: List[str]) -> str:
        """计算总体健康状态"""
        if 'error' in statuses:
            return 'error'
        elif 'unhealthy' in statuses:
            return 'unhealthy'
        elif all(status == 'healthy' for status in statuses):
            return 'healthy'
        else:
            return 'unknown'
    
    def get_cached_status(self) -> Dict[str, Any]:
        """获取缓存的健康状态"""
        cached_status = cache.get(f'{self.CACHE_KEY_PREFIX}:status')
        
        if cached_status:
            return cached_status
        else:
            # 如果没有缓存，执行一次检查
            return self.check_all()
    
    def get_usage_metrics(self, days: int = 7) -> Dict[str, Any]:
        """获取使用指标"""
        try:
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=days)
            
            # 查询使用统计
            stats = AIUsageStatistics.objects.filter(date__gte=start_date, date__lte=end_date).values('date').annotate(
                total_generations=models.Sum('generation_count'),
                total_cases=models.Sum('generated_cases_count'),
                total_accepted=models.Sum('accepted_cases_count')
            ).order_by('date')
            
            # 计算总计
            total_metrics = {
                'total_generations': sum(s['total_generations'] or 0 for s in stats),
                'total_cases': sum(s['total_cases'] or 0 for s in stats),
                'total_accepted': sum(s['total_accepted'] or 0 for s in stats),
                'acceptance_rate': 0
            }
            
            if total_metrics['total_cases'] > 0:
                total_metrics['acceptance_rate'] = round(
                    (total_metrics['total_accepted'] / total_metrics['total_cases']) * 100, 2
                )
            
            return {
                'period': f'{start_date} 至 {end_date}',
                'daily_stats': list(stats),
                'total_metrics': total_metrics
            }
            
        except Exception as e:
            SimpleLogger.exception(f"获取使用指标失败: {str(e)}")

            return {
                'error': str(e),
                'period': f'最近{days}天',
                'daily_stats': [],
                'total_metrics': {
                    'total_generations': 0,
                    'total_cases': 0,
                    'total_accepted': 0,
                    'acceptance_rate': 0
                }
            }


# 全局健康检查器实例
health_checker = AIHealthChecker()

def get_ai_health_status() -> Dict[str, Any]:
    """获取AI服务健康状态的便捷函数"""
    return health_checker.get_cached_status()


def perform_ai_health_check() -> Dict[str, Any]:
    """执行AI服务健康检查的便捷函数"""
    return health_checker.check_all()


def get_ai_usage_metrics(days: int = 7) -> Dict[str, Any]:
    """获取AI使用指标的便捷函数"""
    return health_checker.get_usage_metrics(days)
