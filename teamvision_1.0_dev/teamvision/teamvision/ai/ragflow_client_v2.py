# coding=utf-8
"""
RAGFlow Agent客户端

支持两种工作模式：
1. Chat模式：基于知识库的对话式AI，适合复杂的测试用例生成
2. Agent模式：基于预配置Agent的对话，适合特定场景的快速响应

使用示例：
    # Chat模式
    ragflow_chat_client = RAGFlowClient(mode="chat")
    response = chat_client.ask("请帮我生成登录功能的测试用例")

    # Agent模式
    ragflow_agent_client = RAGFlowClient(mode="agent")
    response = agent_client.ask("请帮我生成测试用例")
"""

from typing import Dict, List, Any
from django.conf import settings
import json

from ragflow_sdk import RAGFlow
from gatesidelib.common.simplelogger import SimpleLogger
from teamvision.ai.services import AITestAgent


class RAGFlowClient:
    """RAGFlow客户端 - 支持chat和agent两种模式"""

    def __init__(self, mode: str = "chat"):
        """
        初始化RAGFlow客户端

        Args:
            mode: 工作模式，支持 'chat' 或 'agent'
        """
        if mode not in ['chat', 'agent']:
            raise ValueError("mode 必须是 'chat' 或 'agent'")

        self.mode = mode
        self.base_url = getattr(settings, 'RAGFLOW_BASE_URL', 'http://localhost:9380')
        self.api_key = getattr(settings, 'RAGFLOW_API_KEY', '')
        self.dataset_name = getattr(settings, 'RAGFLOW_DATASET_NAME', 'testcase_generation')
        self.chat_name = getattr(settings, 'RAGFLOW_CHAT_NAME', 'TestCase Assistant')
        self.agent_id = getattr(settings, 'RAGFLOW_AGENT_ID', '')

        # 初始化RAGFlow SDK客户端
        try:
            self.ragflow_client = RAGFlow(api_key=self.api_key, base_url=self.base_url)
            SimpleLogger.info(f"RAGFlow SDK客户端初始化成功: {self.base_url}, 模式: {self.mode}")
        except Exception as e:
            SimpleLogger.exception(f"RAGFlow SDK客户端初始化失败: {str(e)}")
            raise Exception(f"RAGFlow SDK初始化失败: {str(e)}")

        # 初始化模式相关资源
        self.dataset = None
        self.chat_assistant = None
        self.agent = None
        self.agent_session = None

        # 根据模式初始化相应资源
        self._initialize_resources()

    def _initialize_resources(self):
        """根据模式初始化相应资源"""
        try:
            if self.mode == "chat":
                self._initialize_chat_resources()
            elif self.mode == "agent":
                self._initialize_agent_resources()

            SimpleLogger.info(f"RAGFlow {self.mode} 模式资源初始化完成")
        except Exception as e:
            SimpleLogger.exception(f"RAGFlow {self.mode} 模式资源初始化失败: {str(e)}")
            # 不抛出异常，允许后续手动重试

    def _initialize_chat_resources(self):
        """初始化chat模式资源"""
        # 获取或创建数据集
        self.dataset = self._get_or_create_dataset()

        # 获取或创建聊天助手
        self.chat_assistant = self._get_or_create_chat_assistant()

    def _initialize_agent_resources(self):
        """初始化agent模式资源"""
        if not self.agent_id:
            raise Exception("Agent模式需要配置RAGFLOW_AGENT_ID")

        # 获取agent实例
        agents = self.ragflow_client.list_agents(id=self.agent_id)
        if not agents:
            raise Exception(f"未找到ID为 {self.agent_id} 的Agent")

        self.agent = agents[0]
        SimpleLogger.info(f"Agent初始化成功: {self.agent.name}")

        # 创建agent会话
        self.agent_session = self.agent.create_session()
        SimpleLogger.info("Agent会话创建成功")
    
    def _get_or_create_dataset(self):
        """获取或创建数据集"""
        try:
            # 尝试获取现有数据集
            datasets = self.ragflow_client.list_datasets(name=self.dataset_name)
            if datasets:
                SimpleLogger.info(f"找到现有数据集: {self.dataset_name}")
                return datasets[0]
            
            # 创建新数据集
            SimpleLogger.info(f"创建新数据集: {self.dataset_name}")
            dataset = self.ragflow_client.create_dataset(
                name=self.dataset_name,
                description="用于测试用例生成的知识库",
                chunk_method="qa",  # 使用Q&A模式适合测试用例生成
                embedding_model="BAAI/bge-large-zh-v1.5@BAAI"
            )
            return dataset

        except Exception as e:
            SimpleLogger.exception(f"数据集操作失败: {str(e)}")
            raise Exception(f"数据集操作失败: {str(e)}")

    def _get_or_create_chat_assistant(self):
        """获取或创建聊天助手"""
        try:
            # 尝试获取现有聊天助手
            chats = self.ragflow_client.list_chats(name=self.chat_name)
            if chats:
                SimpleLogger.info(f"找到现有聊天助手: {self.chat_name}")
                return chats[0]

            # 创建新聊天助手
            SimpleLogger.info(f"创建新聊天助手: {self.chat_name}")

            # 配置LLM参数
            llm_config = {
                "model_name": getattr(settings, 'RAGFLOW_LLM_MODEL', None),
                "temperature": 0.1,
                "top_p": 0.3,
                "presence_penalty": 0.2,
                "frequency_penalty": 0.7
            }

            # 配置提示词
            prompt_config = {
                "similarity_threshold": 0.2,
                "keywords_similarity_weight": 0.7,
                "top_n": 8,
                "variables": [{"key": "knowledge", "optional": True}],
                "empty_response": "抱歉，我无法基于当前知识库生成相关的测试用例。请提供更详细的需求描述。",
                "opener": "🎯 请选择需求，基于您的需求，为您生成测试用例",
                "show_quote": True,
                "prompt": AITestAgent.build_system_prompt()
            }

            dataset_ids = [self.dataset.id] if self.dataset else []

            chat = self.ragflow_client.create_chat(
                name=self.chat_name,
                dataset_ids=dataset_ids,
                llm=llm_config,
                prompt=prompt_config
            )
            return chat
            
        except Exception as e:
            SimpleLogger.exception(f"聊天助手操作失败: {str(e)}")
            raise Exception(f"聊天助手操作失败: {str(e)}")

    def ask(self, question: str, session_id: str = None, stream: bool = False) -> str:
        """
        统一的对话接口，支持chat和agent两种模式

        Args:
            question: 用户问题
            session_id: 会话ID（agent模式使用）
            stream: 是否流式返回

        Returns:
            AI回复内容
        """
        try:
            if self.mode == "chat":
                return self._ask_chat(question, stream)
            elif self.mode == "agent":
                return self._ask_agent(question, session_id, stream)
            else:
                raise Exception(f"不支持的模式: {self.mode}")

        except Exception as e:
            SimpleLogger.exception(f"AI对话失败: {str(e)}")
            return f"对话失败: {str(e)}"

    def _ask_chat(self, question: str, stream: bool = False) -> str:
        """Chat模式对话"""
        if not self.chat_assistant:
            self._initialize_chat_resources()

        if not self.chat_assistant:
            raise Exception("Chat助手未初始化")

        # 创建会话
        session = self.chat_assistant.create_session(name="AI对话")
        response = session.ask(question=question, stream=stream)

        return response.content if response else "无响应"

    def _ask_agent(self, question: str, session_id: str = None, stream: bool = False) -> str:
        """Agent模式对话"""
        if not self.agent:
            self._initialize_agent_resources()

        if not self.agent:
            raise Exception("Agent未初始化")

        # 使用指定会话或默认会话
        if session_id:
            session = self.agent.get_session(session_id)
        else:
            session = self.agent_session

        if not session:
            raise Exception("Agent会话未初始化")

        response = session.ask(question=question, stream=stream)
        return response.answer if hasattr(response, 'answer') else response.content

    def create_agent_session(self, name: str = None) -> str:
        """
        创建新的Agent会话

        Args:
            name: 会话名称

        Returns:
            会话ID
        """
        if self.mode != "agent":
            raise Exception("只有Agent模式支持创建会话")

        if not self.agent:
            self._initialize_agent_resources()

        session = self.agent.create_session(name=name)
        return session.id


    def get_agent_session(self, session_id: str):
        """获取Agent会话"""
        if self.mode != "agent":
            raise Exception("只有Agent模式支持获取会话")

        if not self.agent:
            self._initialize_agent_resources()

        return self.agent.get_session(session_id)

    
    def _parse_response(self, response_content: str, request: TestCaseGenerationRequest) -> TestCaseGenerationResponse:
        """解析RAGFlow响应"""
        try:
            if not response_content:
                raise ValueError("RAGFlow返回空响应")

            SimpleLogger.info(f"RAGFlow响应内容: {response_content}")

            # 尝试从响应中提取JSON
            test_cases_data = self._extract_json_from_response(response_content)

            if not test_cases_data or 'test_cases' not in test_cases_data:
                raise ValueError("无法解析测试用例数据")

            # 转换为TestCaseItem对象
            test_cases = []
            for case_data in test_cases_data['test_cases']:
                test_case = TestCaseItem(
                    title=case_data.get('title', ''),
                    description=case_data.get('description', ''),
                    precondition=case_data.get('precondition', ''),
                    test_steps=case_data.get('test_steps', []),
                    expected_result=case_data.get('expected_result', ''),
                    priority=case_data.get('priority', 2),
                    test_type=case_data.get('test_type', request.generation_type),
                    tags=case_data.get('tags', [])
                )
                test_cases.append(test_case)

            return TestCaseGenerationResponse(
                success=True,
                message="测试用例生成成功",
                test_cases=test_cases,
                generation_id="",  # 字符串响应没有ID
                metadata={
                    'generation_time': '',
                    'session_id': '',
                    'request_params': {
                        'generation_type': request.generation_type
                    },
                    'mode': self.mode
                }
            )

        except Exception as e:
            SimpleLogger.exception(f"解析RAGFlow响应失败: {str(e)}")
            return TestCaseGenerationResponse(
                success=False,
                message=f"响应解析失败: {str(e)}",
                test_cases=[],
                generation_id="",
                metadata={}
            )

    def _extract_references(self, response) -> List[Dict]:
        """提取响应中的引用信息"""
        references = []
        if hasattr(response, 'reference') and response.reference:
            for ref in response.reference:
                references.append({
                    'chunk_id': getattr(ref, 'id', ''),
                    'content': getattr(ref, 'content', ''),
                    'document_name': getattr(ref, 'document_name', ''),
                    'similarity': getattr(ref, 'similarity', 0.0)
                })
        return references

    def _extract_json_from_response(self, response_text: str) -> Dict:
        """从响应文本中提取JSON数据"""
        import re

        # 尝试直接解析JSON
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            pass

        # 尝试从markdown代码块中提取JSON
        json_pattern = r'```json\s*(.*?)\s*```'
        matches = re.findall(json_pattern, response_text, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue

        # 尝试从普通代码块中提取JSON
        code_pattern = r'```\s*(.*?)\s*```'
        matches = re.findall(code_pattern, response_text, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue

        # 尝试查找JSON对象
        json_obj_pattern = r'\{.*\}'
        matches = re.findall(json_obj_pattern, response_text, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue

        raise ValueError("无法从响应中提取有效的JSON数据")

    def delete_knowledge_document(self, document_id: str) -> bool:
        """删除知识文档"""
        try:
            if not self.dataset:
                self._initialize_resources()

            if not self.dataset:
                raise Exception("数据集未初始化")

            self.dataset.delete_documents(ids=[document_id])
            SimpleLogger.info(f"成功删除知识文档: {document_id}")

            return True

        except Exception as e:
            SimpleLogger.exception(f"删除知识文档失败: {str(e)}")
            return False

    def get_chat_history(self, session_id: str = None, limit: int = 10) -> List[Dict]:
        """获取聊天历史"""
        try:
            if not self.chat_assistant:
                self._initialize_resources()

            if not self.chat_assistant:
                raise Exception("聊天助手未初始化")

            # 获取会话列表
            sessions = self.chat_assistant.list_sessions(page_size=limit)

            history = []
            for session in sessions:
                if session_id and session.id != session_id:
                    continue

                # 获取会话消息
                for message in session.message:
                    history.append({
                        'session_id': session.id,
                        'session_name': session.name,
                        'role': getattr(message, 'role', 'assistant'),
                        'content': getattr(message, 'content', ''),
                        'timestamp': getattr(message, 'create_time', '')
                    })

            return history

        except Exception as e:
            SimpleLogger.exception(f"获取聊天历史失败: {str(e)}")
            return []

    def health_check(self) -> Dict[str, Any]:
        """健康检查 - 支持chat和agent两种模式"""
        try:
            # 检查客户端连接
            if self.mode == "chat":
                self.ragflow_client.list_datasets(page_size=1)
            else:  # agent模式
                self.ragflow_client.list_agents(page_size=1)

            # 检查资源状态
            result = {
                'status': 'healthy',
                'client_connected': True,
                'mode': self.mode,
                'base_url': self.base_url
            }

            if self.mode == "chat":
                result.update({
                    'dataset_status': "OK" if self.dataset else "NOT_INITIALIZED",
                    'chat_assistant_status': "OK" if self.chat_assistant else "NOT_INITIALIZED",
                    'dataset_name': self.dataset_name,
                    'chat_name': self.chat_name
                })
            elif self.mode == "agent":
                result.update({
                    'agent_status': "OK" if self.agent else "NOT_INITIALIZED",
                    'agent_session_status': "OK" if self.agent_session else "NOT_INITIALIZED",
                    'agent_id': self.agent_id
                })

            return result

        except Exception as e:
            SimpleLogger.exception(f"健康检查失败: {str(e)}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'client_connected': False,
                'mode': self.mode
            }


# 全局RAGFlow客户端实例 - 默认使用chat模式
ragflow_client = RAGFlowClient(mode="chat")

# 预定义的客户端实例
ragflow_chat_client = RAGFlowClient(mode="chat")
ragflow_agent_client = RAGFlowClient(mode="agent")
