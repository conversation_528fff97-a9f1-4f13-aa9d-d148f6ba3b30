#!/usr/bin/env python
# coding=utf-8
"""
测试 AI API V2版本
"""

import requests
import json
import sys

# API 基础URL
BASE_URL = "http://localhost:8000/api/ai"

def test_simple_chat_v2():
    """测试简单对话接口 V2"""
    print("=" * 60)
    print("测试简单对话接口 V2")
    print("=" * 60)
    
    url = f"{BASE_URL}/v2/simple-chat/"
    
    # 测试默认消息
    data = {
        "message": "请帮我生成测试用例"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ 请求成功")
            print(f"版本: {result.get('version', 'unknown')}")
            print(f"响应: {result.get('response', '')}")
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def test_service_status_v2():
    """测试服务状态 V2"""
    print("\n" + "=" * 60)
    print("测试服务状态 V2")
    print("=" * 60)
    
    url = f"{BASE_URL}/v2/status/"
    
    try:
        response = requests.get(url)
        print(f"请求URL: {url}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ 请求成功")
            print(f"版本: {result.get('version', 'unknown')}")
            print(f"状态: {result.get('status', 'unknown')}")
            print(f"健康详情: {json.dumps(result.get('health_details', {}), ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def test_model_info_v2():
    """测试模型信息 V2"""
    print("\n" + "=" * 60)
    print("测试模型信息 V2")
    print("=" * 60)
    
    url = f"{BASE_URL}/v2/model-info/"
    
    try:
        response = requests.get(url)
        print(f"请求URL: {url}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ 请求成功")
            print(f"模型信息: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def test_knowledge_management_v2():
    """测试知识库管理 V2"""
    print("\n" + "=" * 60)
    print("测试知识库管理 V2")
    print("=" * 60)
    
    url = f"{BASE_URL}/v2/knowledge/"
    
    try:
        response = requests.get(url)
        print(f"请求URL: {url}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ 请求成功")
            print(f"版本: {result.get('version', 'unknown')}")
            print(f"文档数量: {result.get('count', 0)}")
            print(f"文档列表: {json.dumps(result.get('documents', []), ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def test_generate_testcases_v2():
    """测试测试用例生成 V2"""
    print("\n" + "=" * 60)
    print("测试测试用例生成 V2")
    print("=" * 60)
    
    url = f"{BASE_URL}/v2/generate/"
    
    data = {
        "project": 1,  # 假设项目ID为1
        "requirement_description": "用户登录功能：用户可以通过用户名和密码登录系统，登录成功后跳转到主页面，登录失败显示错误信息",
        "generation_type": "functional"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ 请求成功")
            print(f"版本: {result.get('version', 'unknown')}")
            print(f"生成数量: {result.get('generated_count', 0)}")
            print(f"会话ID: {result.get('session_id', '')}")
            
            test_cases = result.get('test_cases', [])
            for i, case in enumerate(test_cases[:2], 1):  # 只显示前2个
                print(f"\n测试用例 {i}:")
                print(f"  标题: {case.get('title', '')}")
                print(f"  描述: {case.get('description', '')}")
                print(f"  前置条件: {case.get('precondition', '')}")
                print(f"  测试步骤: {case.get('test_steps', [])}")
                print(f"  预期结果: {case.get('expected_result', '')}")
                
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def main():
    """主函数"""
    print("AI API V2版本测试")
    print("注意：请确保Django服务器正在运行")
    
    # 运行所有测试
    test_simple_chat_v2()
    test_service_status_v2()
    test_model_info_v2()
    test_knowledge_management_v2()
    
    # 测试用例生成需要认证，暂时跳过
    print("\n注意：测试用例生成接口需要用户认证，请在有认证的环境中测试")
    
    print("\n" + "=" * 60)
    print("✓ AI API V2版本测试完成")
    print("=" * 60)


if __name__ == "__main__":
    main()
