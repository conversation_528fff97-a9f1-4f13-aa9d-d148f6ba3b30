# TeamVision AI后端集成说明

## 概述

本文档说明如何在TeamVision后端集成RAGFlow Agent，实现AI智能测试用例生成功能。

## 功能特性

### 🤖 核心AI功能
- **智能测试用例生成**: 根据需求描述自动生成完整测试用例
- **测试用例优化**: AI分析和优化现有测试用例
- **覆盖度分析**: 智能分析测试覆盖度并提供改进建议
- **对话式交互**: 支持与AI助手进行自然语言对话

### 📊 管理功能
- **会话管理**: 管理AI生成会话和历史记录
- **批量操作**: 支持批量采纳、删除、评分测试用例
- **使用统计**: 详细的AI使用统计和分析
- **配置管理**: 灵活的AI配置和提示词管理

## 安装配置

### 1. 安装依赖

```bash
pip install requests==2.31.0
pip install openai==1.3.0
pip install httpx==0.25.0
pip install pydantic==2.5.0
```

### 2. 配置RAGFlow

在 `.env` 文件中添加RAGFlow配置：

```bash
# RAGFlow配置
RAGFLOW_BASE_URL=http://your-ragflow-server:9380
RAGFLOW_API_KEY=your_api_key_here
RAGFLOW_AGENT_ID=your_agent_id_here
RAGFLOW_TIMEOUT=30
```

### 3. 数据库迁移

```bash
python manage.py makemigrations ai
python manage.py migrate
```

### 4. 初始化AI数据

```bash
python manage.py init_ai_data --create-default-config --test-ragflow-connection
```

## API接口

### 核心接口

#### 1. 生成测试用例
```http
POST /api/ai/generate/
Content-Type: application/json

{
    "project_id": 1,
    "requirement_description": "用户登录功能需求",
    "module_name": "用户管理",
    "generation_type": "functional",
    "case_count": 10,
    "priority_level": "medium"
}
```

#### 2. 采纳测试用例
```http
POST /api/ai/accept/
Content-Type: application/json

{
    "ai_case_ids": [1, 2, 3],
    "parent_id": 0,
    "module_id": 1
}
```

#### 3. 优化测试用例
```http
POST /api/ai/optimize/
Content-Type: application/json

{
    "ai_case_id": 1,
    "optimization_type": "quality"
}
```

#### 4. AI对话
```http
POST /api/ai/chat/
Content-Type: application/json

{
    "session_id": "ai_gen_abc123",
    "message": "请帮我优化这个测试用例"
}
```

### 管理接口

#### 1. 获取AI会话列表
```http
GET /api/ai/sessions/?project_id=1&status=completed
```

#### 2. 获取生成的测试用例
```http
GET /api/ai/sessions/{session_id}/cases/?status=generated
```

#### 3. 获取对话历史
```http
GET /api/ai/sessions/{session_id}/history/
```

#### 4. 使用统计
```http
GET /api/ai/statistics/?project_id=1&date_from=2024-01-01
```

## 前端集成

### 1. 引入AI API服务

```javascript
import aiApi from '@/api/ai'

// 生成测试用例
const response = await aiApi.quickGenerateTestCasesApi(
    projectId, 
    requirementDescription, 
    {
        module_name: '用户管理',
        generation_type: 'functional',
        case_count: 10
    }
)

// 采纳测试用例
await aiApi.batchAcceptTestCasesApi([1, 2, 3], parentId, moduleId)
```

### 2. 与AIAssistant.vue组件集成

```javascript
// 在AIAssistant.vue中调用后端API
methods: {
    async generateTestCases() {
        try {
            const response = await aiApi.generateTestCasesApi({
                project_id: this.projectId,
                requirement_description: this.requirementText,
                module_name: this.moduleName,
                generation_type: this.generationType,
                case_count: this.caseCount
            })
            
            if (response.data.success) {
                this.generatedCases = response.data.test_cases
                this.sessionId = response.data.session_id
            }
        } catch (error) {
            this.$Message.error('生成失败: ' + error.message)
        }
    },
    
    async acceptTestCase(caseId) {
        try {
            await aiApi.batchAcceptTestCasesApi([caseId])
            this.$Message.success('测试用例已采纳')
        } catch (error) {
            this.$Message.error('采纳失败: ' + error.message)
        }
    }
}
```

## 数据模型

### 主要模型

1. **AIGenerationSession**: AI生成会话
2. **AIGeneratedTestCase**: AI生成的测试用例
3. **AIChatHistory**: AI对话历史
4. **AIConfiguration**: AI配置
5. **AIUsageStatistics**: AI使用统计

### 数据流程

```
用户请求 → AI生成会话 → 调用RAGFlow → 生成测试用例 → 用户评审 → 采纳到项目
```

## RAGFlow集成

### 客户端配置

RAGFlow客户端位于 `teamvision/ai/ragflow_client.py`，主要功能：

1. **连接管理**: 管理与RAGFlow服务的HTTP连接
2. **请求构建**: 构建符合RAGFlow API规范的请求
3. **响应解析**: 解析RAGFlow返回的AI生成内容
4. **错误处理**: 处理网络错误和API错误

### 提示词工程

系统内置多种提示词模板：

- **功能测试模板**: 生成功能测试用例
- **边界测试模板**: 生成边界条件测试用例
- **异常测试模板**: 生成异常处理测试用例
- **集成测试模板**: 生成集成测试用例

## 监控和日志

### 日志记录

所有AI操作都会记录详细日志：

```python
import logging
logger = logging.getLogger(__name__)

logger.info(f"AI生成会话创建: {session.session_id}")
logger.error(f"RAGFlow API调用失败: {str(e)}")
```

### 使用统计

系统自动统计：
- 生成次数和成功率
- 用例采纳率
- API调用统计
- 用户使用情况

## 性能优化

### 1. 缓存策略
- 缓存常用的AI配置
- 缓存生成结果避免重复调用

### 2. 异步处理
- 长时间的AI生成任务异步处理
- 支持任务状态查询

### 3. 限流控制
- 限制单用户请求频率
- 限制单次生成用例数量

## 故障排除

### 常见问题

1. **RAGFlow连接失败**
   - 检查RAGFLOW_BASE_URL配置
   - 确认RAGFlow服务是否正常运行
   - 验证API_KEY是否正确

2. **生成结果解析失败**
   - 检查RAGFlow返回格式
   - 调整提示词模板
   - 查看详细错误日志

3. **数据库迁移失败**
   - 确认数据库连接正常
   - 检查模型定义是否正确
   - 手动执行迁移命令

### 调试模式

开启调试模式查看详细信息：

```python
# settings.py
LOGGING = {
    'loggers': {
        'teamvision.ai': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': True,
        },
    }
}
```

## 扩展开发

### 添加新的AI功能

1. 在 `services.py` 中添加业务逻辑
2. 在 `views.py` 中添加API视图
3. 在 `urls.py` 中添加路由
4. 在前端 `ai.js` 中添加API调用

### 自定义提示词

```python
# 添加新的提示词模板
AIConfiguration.objects.create(
    config_type='prompt',
    config_key='custom_template',
    config_value={
        'name': '自定义模板',
        'template': '你的提示词模板...'
    }
)
```

## 安全考虑

1. **API访问控制**: 所有AI接口都需要用户认证
2. **输入验证**: 严格验证用户输入参数
3. **输出过滤**: 过滤AI生成内容中的敏感信息
4. **使用限制**: 限制用户的AI使用频率和数量

## 总结

通过以上配置，您可以成功将RAGFlow Agent集成到TeamVision后端，实现智能测试用例生成功能。系统提供了完整的API接口、数据管理、监控统计等功能，可以大大提升测试用例编写的效率和质量。
