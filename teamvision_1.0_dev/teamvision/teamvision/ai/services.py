# coding=utf-8
"""
AI服务业务逻辑
"""

import uuid
import json
from datetime import datetime
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from django.contrib.auth.models import User
from django.db import transaction
from django.utils import timezone

from gatesidelib.common.simplelogger import SimpleLogger
from teamvision.project.models import Project, ProjectTestCase
from teamvision.ai.models import (
    AIGenerationSession, AIGeneratedTestCase, 
    AIChatHistory, AIUsageStatistics
)
from teamvision.ai.ragflow_client_v2 import ragflow_agent_client


@dataclass
class TestCaseGenerationRequest:
    """测试用例生成请求"""
    requirement_description: str  # 需求描述
    generation_type: str  # 生成类型：'functional', 'boundary', 'exception', 'integration'
    case_count: int = 10  # 生成数量，默认5个


@dataclass
class TestCaseItem:
    """单个测试用例"""
    title: str
    description: str
    precondition: str
    test_steps: List[str]
    expected_result: str
    priority: int
    test_type: str
    tags: List[str]


@dataclass
class TestCaseGenerationResponse:
    """测试用例生成响应"""
    success: bool
    message: str
    test_cases: List[TestCaseItem]
    generation_id: str
    metadata: Dict[str, Any]


class AITestCaseService:
    """AI测试 服务"""
    
    @staticmethod
    def create_generation_session(request_data: Dict, user: User, ) -> AIGenerationSession:
        """创建AI生成会话"""
        
        session_id = f"ai_gen_{uuid.uuid4().hex[:12]}"
        
        session = AIGenerationSession.objects.create(
            session_id=session_id,
            user=user,
            project=request_data['project'],
            requirement_description=request_data['requirement_description'],
            generation_type=request_data['generation_type'],
            status='active'
        )
        
        # 记录系统消息
        AIChatHistory.objects.create(
            session=session,
            message_type='system',
            content=f"开始生成测试用例会话：{session.requirement_description}",
            metadata={
                'action': 'session_created',
                'parameters': {
                    'generation_type': session.generation_type,
                }
            }
        )
        return session
    
    @staticmethod
    def generate_test_cases(session: AIGenerationSession) -> TestCaseGenerationResponse:
        """生成测试用例"""
        
        try:
            # 构建生成请求
            request = TestCaseGenerationRequest(
                requirement_description=session.requirement_description,
                generation_type=session.generation_type,
            )
            
            # 记录用户请求
            case_count = 0
            AIChatHistory.objects.create(
                session=session,
                message_type='user',
                content=f"请为模块'{session.requirement_description}'生成{case_count}个{session.generation_type}测试用例",
                metadata={'request_params': request.__dict__}
            )
            
            # 调用RAGFlow生成
            response = ragflow_agent_client.generate_test_cases(request)

            if response.success:
                # 保存生成的测试用例
                with transaction.atomic():
                    generated_cases = []
                    for test_case in response.test_cases:
                        ai_case = AIGeneratedTestCase.objects.create(
                            session=session,
                            title=test_case.title,
                            description=test_case.description,
                            precondition=test_case.precondition,
                            test_steps=test_case.test_steps,
                            expected_result=test_case.expected_result,
                            priority=test_case.priority,
                            test_type=test_case.test_type,
                            tags=test_case.tags,
                            status='generated',
                            metadata=response.metadata
                        )
                        generated_cases.append(ai_case)
                    
                    # 更新会话状态
                    session.ragflow_generation_id = response.generation_id
                    session.generated_count = len(generated_cases)
                    session.status = 'completed'
                    session.completed_time = timezone.now()
                    session.metadata.update(response.metadata)
                    session.save()
                
                # 记录AI响应
                AIChatHistory.objects.create(
                    session=session,
                    message_type='assistant',
                    content=f"已成功生成{len(response.test_cases)}个测试用例",
                    metadata={
                        'generation_id': response.generation_id,
                        'generated_count': len(response.test_cases)
                    }
                )
                
                # 更新使用统计
                AITestCaseService._update_usage_statistics(
                    session.user, 
                    session.project, 
                    generation_count=1,
                    generated_cases_count=len(response.test_cases)
                )
                
            else:
                # 生成失败
                session.status = 'failed'
                session.save()
                
                # 记录错误消息
                AIChatHistory.objects.create(
                    session=session,
                    message_type='system',
                    content=f"测试用例生成失败: {response.message}",
                    metadata={'error': response.message}
                )
            
            return response
            
        except Exception as e:
            SimpleLogger.exception(f"生成测试用例异常: {str(e)}")
            
            # 更新会话状态
            session.status = 'failed'
            session.save()
            
            # 记录错误
            AIChatHistory.objects.create(
                session=session,
                message_type='system',
                content=f"生成过程发生异常: {str(e)}",
                metadata={'exception': str(e)}
            )
            
            return TestCaseGenerationResponse(
                success=False,
                message=f"生成异常: {str(e)}",
                test_cases=[],
                generation_id="",
                metadata={}
            )
    
    @staticmethod
    def accept_generated_test_case(ai_case: AIGeneratedTestCase, parent_id: int = 0, module_id: int = 0) -> ProjectTestCase:
        """采纳AI生成的测试用例"""
        
        try:
            with transaction.atomic():
                # 创建正式的测试用例
                project_case = ProjectTestCase.objects.create(
                    Title=ai_case.title,
                    Desc=ai_case.description,
                    Precondition=ai_case.precondition,
                    ExpectResult=ai_case.expected_result,
                    Priority=ai_case.priority,
                    Parent=parent_id,
                    Project=ai_case.session.project.id,
                    Module=module_id,
                    IsGroup=False,
                    Status=1,  # 启用状态
                    Scenes=json.dumps(ai_case.test_steps) if ai_case.test_steps else "[]"
                )
                
                # 更新AI生成用例状态
                ai_case.status = 'accepted'
                ai_case.project_test_case = project_case
                ai_case.save()
                
                # 更新会话统计
                session = ai_case.session
                session.accepted_count += 1
                session.save()
                
                # 记录采纳消息
                AIChatHistory.objects.create(
                    session=session,
                    message_type='system',
                    content=f"测试用例'{ai_case.title}'已被采纳",
                    metadata={
                        'action': 'case_accepted',
                        'ai_case_id': ai_case.id,
                        'project_case_id': project_case.id
                    }
                )
                
                # 更新使用统计
                AITestCaseService._update_usage_statistics(
                    session.user,
                    session.project,
                    accepted_cases_count=1
                )
                
                return project_case
                
        except Exception as e:
            SimpleLogger.exception(f"采纳测试用例失败: {str(e)}")
            raise Exception(f"采纳失败: {str(e)}")
    
    @staticmethod
    def batch_accept_test_cases(ai_case_ids: List[int], parent_id: int = 0, module_id: int = 0) -> List[ProjectTestCase]:
        """批量采纳测试用例"""
        
        accepted_cases = []
        
        for ai_case_id in ai_case_ids:
            try:
                ai_case = AIGeneratedTestCase.objects.get(id=ai_case_id)
                project_case = AITestCaseService.accept_generated_test_case(
                    ai_case, parent_id, module_id
                )
                accepted_cases.append(project_case)
            except Exception as e:
                SimpleLogger.exception(f"批量采纳用例失败: {str(e)}")
                continue
        
        return accepted_cases
    
    @staticmethod
    def optimize_test_case(ai_case: AIGeneratedTestCase, optimization_type: str = 'quality') -> Dict:
        """优化测试用例"""
        
        try:
            # 构建测试用例数据
            test_case_data = {
                'title': ai_case.title,
                'description': ai_case.description,
                'precondition': ai_case.precondition,
                'test_steps': ai_case.test_steps,
                'expected_result': ai_case.expected_result,
                'priority': ai_case.priority,
                'test_type': ai_case.test_type,
                'tags': ai_case.tags
            }
            
            # 调用RAGFlow优化
            optimized_data = ragflow_agent_client.optimize_test_case(test_case_data, optimization_type)
            
            # 记录优化历史
            AIChatHistory.objects.create(
                session=ai_case.session,
                message_type='user',
                content=f"请优化测试用例'{ai_case.title}'",
                metadata={
                    'action': 'optimize_request',
                    'optimization_type': optimization_type,
                    'original_case_id': ai_case.id
                }
            )
            
            AIChatHistory.objects.create(
                session=ai_case.session,
                message_type='assistant',
                content="测试用例优化完成",
                metadata={
                    'action': 'optimize_response',
                    'optimized_data': optimized_data
                }
            )
            
            return optimized_data
            
        except Exception as e:
            SimpleLogger.exception(f"测试用例优化失败: {str(e)}")
            return {'error': str(e)}
    
    @staticmethod
    def chat_with_ai(session: AIGenerationSession, user_message: str) -> str:
        """与AI对话"""

        try:
            # 记录用户消息
            AIChatHistory.objects.create(
                session=session,
                message_type='user',
                content=user_message
            )

            # 智能分析用户消息并生成相应响应
            ai_response = AITestCaseService._generate_smart_response(user_message, session)

            # 记录AI响应
            AIChatHistory.objects.create(
                session=session,
                message_type='assistant',
                content=ai_response
            )

            # 更新使用统计
            AITestCaseService._update_usage_statistics(
                session.user,
                session.project,
                chat_messages_count=2  # 用户消息 + AI响应
            )

            return ai_response

        except Exception as e:
            SimpleLogger.exception(f"AI对话失败: {str(e)}")
            return f"对话失败: {str(e)}"

    @staticmethod
    def _generate_smart_response(user_message: str, session: AIGenerationSession) -> str:
        """生成智能响应"""

        # 去除首尾空格并转换为小写进行匹配
        trimmed_message = user_message.strip()

        # 精确匹配特定消息
        if trimmed_message == '请帮我生成测试用例':
            return "<h4>请选择需求</h4><p>我将会基于您的需求，为您生成相关的测试用例。</p>"

        # 其他消息的智能匹配
        lower_message = trimmed_message.lower()

        if '生成' in lower_message and '测试用例' in lower_message:
            return "我可以帮您生成测试用例。请先选择具体的需求，然后我会为您生成相应的测试用例。"
        elif '分析' in lower_message and '用例' in lower_message:
            return "我可以帮您分析测试用例的完整性和质量。请提供具体的测试用例信息。"
        elif '补全' in lower_message or '完善' in lower_message:
            return "我可以帮您补全和完善测试用例。请提供需要补全的测试用例详情。"
        elif '覆盖' in lower_message or '覆盖度' in lower_message:
            return "我可以帮您分析测试覆盖度。请提供需求描述，我会分析当前测试用例的覆盖情况。"
        else:
            return f"收到您的消息：{user_message}。我是AI测试助手，可以帮您生成测试用例、分析用例质量、补全用例内容等。请告诉我您需要什么帮助？"

    @staticmethod
    def get_session_history(session: AIGenerationSession) -> List[Dict]:
        """获取会话历史"""
        
        history = AIChatHistory.objects.filter(session=session).order_by('created_time')
        
        return [
            {
                'id': msg.id,
                'type': msg.message_type,
                'content': msg.content,
                'created_time': msg.created_time.strftime('%Y-%m-%d %H:%M:%S'),
                'metadata': msg.metadata
            }
            for msg in history
        ]
    
    @staticmethod
    def _update_usage_statistics(user: User, project: Project, generation_count: int = 0, generated_cases_count: int = 0, accepted_cases_count: int = 0, 
                                 chat_messages_count: int = 0, api_calls_count: int = 0, api_success_count: int = 0, api_error_count: int = 0):
        """更新使用统计"""
        
        today = timezone.now().date()
        
        stats, created = AIUsageStatistics.objects.get_or_create(
            user=user,
            project=project,
            date=today,
            defaults={
                'generation_count': 0,
                'generated_cases_count': 0,
                'accepted_cases_count': 0,
                'chat_messages_count': 0,
                'api_calls_count': 0,
                'api_success_count': 0,
                'api_error_count': 0,
                'total_generation_time': 0,
                'average_generation_time': 0.0
            }
        )
        
        # 更新统计数据
        stats.generation_count += generation_count
        stats.generated_cases_count += generated_cases_count
        stats.accepted_cases_count += accepted_cases_count
        stats.chat_messages_count += chat_messages_count
        stats.api_calls_count += api_calls_count
        stats.api_success_count += api_success_count
        stats.api_error_count += api_error_count
        
        stats.save()


class AIAnalysisService:
    """AI分析服务"""
    
    @staticmethod
    def analyze_test_coverage(project: Project, requirement_text: str) -> Dict:
        """分析测试用例库覆盖度"""
        
        try:
            # 获取项目的所有测试用例
            test_cases = ProjectTestCase.objects.filter(
                Project=project.id,
                IsGroup=False,
                Status=1
            )

            # 构建测试用例数据
            test_cases_data = []
            for case in test_cases:
                test_cases_data.append({
                    'title': case.Title,
                    'description': case.Desc,
                    'precondition': case.Precondition,
                    'expected_result': case.ExpectResult,
                    'priority': case.Priority
                })
            
            # 调用RAGFlow分析
            analysis_result = ragflow_agent_client.analyze_test_coverage(
                requirement_text, 
                test_cases_data
            )
            
            return analysis_result
            
        except Exception as e:
            SimpleLogger.exception(f"测试覆盖度分析失败: {str(e)}")
            return {
                'error': str(e),
                'coverage_percentage': 0,
                'missing_scenarios': [],
                'suggestions': []
            }


class AITestAgent:
    """AI测试 Agent"""

    def build_test_case_prompt(self) -> str:
        """构建系统提示词"""
        return """
你是一个专业的软件测试工程师，专门负责根据需求描述生成高质量的测试用例。

## 你的职责
1. 分析用户提供的需求描述
2. 基于需求生成全面、准确的测试用例
3. 确保测试用例覆盖主要功能点、边界条件和异常场景
4. 提供结构化的测试用例输出

## 测试用例生成规则
1. 每个测试用例必须包含：标题、描述、前置条件(测试步骤)、预期结果
2. 预期结果要明确、可验证，测试步骤要具体、可执行、易理解
3. 根据重要性设置优先级（1-高，2-中，3-低）
4. 为测试用例添加合适的标签
5. 当知识库中的所有内容都与问题无关时，不得编造内容。

## 输出格式
请严格按照以下JSON格式输出：

```json
{
  "test_cases": [
    {
      "Title": "测试用例标题",
      "description": "测试用例描述",
      "Precondition": "前置条件, 步骤",
      "ExpectResult": "预期结果",
      "priority": 1,
      "IsGroup": true,
    }
  ]
}
```

## 知识库内容
{knowledge}

请基于以上知识库内容和用户需求，生成相应的测试用例。
        """.strip()
    
    def build_optimize_test_case_prompt(test_case_data: Dict, optimization_type: str = 'quality') -> str:
        prompt = f"""
请优化以下测试用例，优化类型：{optimization_type}

原测试用例：
{json.dumps(test_case_data, ensure_ascii=False, indent=2)}

请从以下方面进行优化：
1. 测试步骤的完整性和可执行性
2. 预期结果的明确性
3. 边界条件的覆盖
4. 异常场景的考虑

请返回优化后的测试用例，格式与原格式相同。
            """
        return prompt


    def build_analyze_test_coverage_prompt(requirement_text: str, test_cases: List[Dict]) -> str:
        prompt = f"""
请分析以下测试用例对需求的覆盖情况：

需求描述：
{requirement_text}

测试用例：
{test_cases_text}

请从以下维度分析：
1. 功能覆盖度（百分比）
2. 边界条件覆盖
3. 异常场景覆盖
4. 缺失的测试场景
5. 改进建议

请以JSON格式返回分析结果：
```json
{{
  "coverage_percentage": 85,
  "functional_coverage": "良好",
  "boundary_coverage": "需要改进",
  "exception_coverage": "一般",
  "missing_scenarios": ["场景1", "场景2"],
  "suggestions": ["建议1", "建议2"]
}}
```
            """
        return prompt


    def analyze_test_coverage(self, requirement_text: str, test_cases: List[Dict]) -> Dict:
        """分析测试覆盖度"""
        try:
            if not self.chat_assistant:
                self._initialize_resources()

            if not self.chat_assistant:
                raise Exception("聊天助手未初始化")

            test_cases_text = json.dumps(test_cases, ensure_ascii=False, indent=2)

            prompt =  AITestAgent.build_analyze_test_coverage_prompt(requirement_text, test_cases_text)

            session = self.chat_assistant.create_session(name="测试覆盖度分析")
            response = session.ask(question=prompt, stream=False)

            if response and response.content:
                analysis_data = self._extract_json_from_response(response.content)
                return analysis_data
            else:
                return {
                    'coverage_percentage': 0,
                    'missing_scenarios': [],
                    'suggestions': []
                }

        except Exception as e:
            SimpleLogger.exception(f"测试覆盖度分析失败: {str(e)}")
            return {
                'error': str(e),
                'coverage_percentage': 0,
                'missing_scenarios': [],
                'suggestions': []
            }


    def generate_test_cases(self, request: TestCaseGenerationRequest) -> TestCaseGenerationResponse:
        """生成测试用例 - 支持chat和agent两种模式"""
        try:
            # 构建用户查询
            user_query = self._build_user_query(request)

            # 发送请求并获取响应
            SimpleLogger.info(f"发送测试用例生成请求 ({self.mode}模式): {user_query}")

            if self.mode == "chat":
                response_content = self._generate_test_cases_chat(user_query, request)
            elif self.mode == "agent":
                response_content = self._generate_test_cases_agent(user_query, request)
            else:
                raise Exception(f"不支持的模式: {self.mode}")

            # 解析响应
            return self._parse_response(response_content, request)

        except Exception as e:
            SimpleLogger.exception(f"生成测试用例失败: {str(e)}")
            return TestCaseGenerationResponse(
                success=False,
                message=f"生成失败: {str(e)}",
                test_cases=[],
                generation_id="",
                metadata={}
            )
        
    def _generate_test_cases_chat(self, user_query: str, request: TestCaseGenerationRequest) -> str:
        """使用Chat模式生成测试用例"""
        if not self.chat_assistant:
            self._initialize_chat_resources()

        if not self.chat_assistant:
            raise Exception("Chat助手未初始化")

        # 创建会话
        session = self.chat_assistant.create_session(name=f"测试用例生成_{request.generation_type}")

        # 发送请求并获取响应
        response = session.ask(question=user_query, stream=False)
        return response.content if response else ""

    def _generate_test_cases_agent(self, user_query: str, request: TestCaseGenerationRequest) -> str:
        """使用Agent模式生成测试用例"""
        # request参数保留用于接口一致性，Agent模式主要依赖user_query
        if not self.agent:
            self._initialize_agent_resources()

        if not self.agent or not self.agent_session:
            raise Exception("Agent未初始化")

        # 使用默认会话发送请求
        response = self.agent_session.ask(question=user_query, stream=False)
        return response.answer if hasattr(response, 'answer') else response.content
    

    def _build_user_query(self, request: TestCaseGenerationRequest) -> str:
        """构建用户查询"""
        query_template = """
请帮我生成测试用例。

## 需求信息
**需求描述**: {requirement_description}
**测试类型**: {generation_type}
**生成数量**: {case_count}个

## 生成要求
请根据以上需求信息，生成对应的测试用例。确保：
1. 覆盖主要功能场景
2. 包含边界条件测试
3. 考虑异常情况处理
4. 测试步骤具体可执行
5. 预期结果明确可验证

请按照系统提示中的JSON格式输出测试用例。
        """.format(
            requirement_description=request.requirement_description,
            generation_type=request.generation_type,
            case_count=request.case_count
        )
        
        return query_template.strip()


    def optimize_test_case(self, test_case_data: Dict, optimization_type: str = 'quality') -> Dict:
        """优化测试用例 - 支持chat和agent两种模式"""
        try:
            prompt = AITestAgent.build_optimize_test_case_prompt(test_case_data, optimization_type)

            # 使用统一的ask接口
            response_content = self.ask(prompt)

            if response_content and not response_content.startswith("对话失败"):
                optimized_data = self._extract_json_from_response(response_content)
                return optimized_data
            else:
                return test_case_data

        except Exception as e:
            SimpleLogger.exception(f"测试用例优化失败: {str(e)}")
            return test_case_data  # 返回原数据
