#!/usr/bin/env python
# coding=utf-8
"""
RAGFlow客户端V2测试脚本
测试chat和agent两种模式的功能
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teamvision.settings')
django.setup()

from teamvision.ai.ragflow_client_v2 import (
    RAGFlowClient, 
    TestCaseGenerationRequest,
    create_ragflow_client
)


def test_chat_mode():
    """测试Chat模式"""
    print("=== 测试Chat模式 ===")
    
    try:
        # 创建Chat模式客户端
        chat_client = create_ragflow_client(mode="chat")
        
        # 健康检查
        health = chat_client.health_check()
        print(f"Chat模式健康检查: {health}")
        
        # 简单对话测试
        response = chat_client.ask("你好，请介绍一下你的功能")
        print(f"Chat对话响应: {response}")
        
        # 测试用例生成
        request = TestCaseGenerationRequest(
            requirement_description="用户登录功能，包括用户名密码验证",
            generation_type="functional",
            case_count=3
        )
        
        result = chat_client.generate_test_cases(request)
        print(f"Chat模式测试用例生成结果: {result.success}")
        if result.success:
            print(f"生成了 {len(result.test_cases)} 个测试用例")
            for i, case in enumerate(result.test_cases, 1):
                print(f"  {i}. {case.title}")
        
    except Exception as e:
        print(f"Chat模式测试失败: {str(e)}")


def test_agent_mode():
    """测试Agent模式"""
    print("\n=== 测试Agent模式 ===")
    
    try:
        # 创建Agent模式客户端
        agent_client = create_ragflow_client(mode="agent")
        
        # 健康检查
        health = agent_client.health_check()
        print(f"Agent模式健康检查: {health}")
        
        # 简单对话测试
        response = agent_client.ask("请帮我生成测试用例")
        print(f"Agent对话响应: {response}")
        
        # 创建新会话测试
        session_id = agent_client.create_agent_session("测试会话")
        print(f"创建Agent会话: {session_id}")
        
        # 使用指定会话对话
        response2 = agent_client.ask("生成登录功能的测试用例", session_id=session_id)
        print(f"指定会话对话响应: {response2}")
        
        # 测试用例生成
        request = TestCaseGenerationRequest(
            requirement_description="用户注册功能，包括邮箱验证",
            generation_type="functional",
            case_count=3
        )
        
        result = agent_client.generate_test_cases(request)
        print(f"Agent模式测试用例生成结果: {result.success}")
        if result.success:
            print(f"生成了 {len(result.test_cases)} 个测试用例")
            for i, case in enumerate(result.test_cases, 1):
                print(f"  {i}. {case.title}")
        
    except Exception as e:
        print(f"Agent模式测试失败: {str(e)}")


def test_mode_comparison():
    """比较两种模式的性能"""
    print("\n=== 模式比较测试 ===")
    
    test_question = "请帮我生成一个简单的测试用例"
    
    # Chat模式
    try:
        chat_client = create_ragflow_client(mode="chat")
        chat_response = chat_client.ask(test_question)
        print(f"Chat模式响应长度: {len(chat_response)}")
    except Exception as e:
        print(f"Chat模式比较测试失败: {str(e)}")
    
    # Agent模式
    try:
        agent_client = create_ragflow_client(mode="agent")
        agent_response = agent_client.ask(test_question)
        print(f"Agent模式响应长度: {len(agent_response)}")
    except Exception as e:
        print(f"Agent模式比较测试失败: {str(e)}")


def main():
    """主测试函数"""
    print("开始RAGFlow客户端V2测试...")
    
    # 测试Chat模式
    test_chat_mode()
    
    # 测试Agent模式
    test_agent_mode()
    
    # 模式比较
    test_mode_comparison()
    
    print("\n测试完成!")


if __name__ == "__main__":
    main()
