#!/usr/bin/env python
# coding=utf-8
"""
测试AI对话功能
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/Users/<USER>/idp/kscloud/code/teamvision/teamvision_ezone/teamvision_1.0_dev/teamvision')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teamvision.settings')
django.setup()

from django.contrib.auth.models import User
from teamvision.project.models import Project
from teamvision.ai.services import AITestCaseService


def test_ai_chat():
    """测试AI对话功能"""
    
    # 获取测试用户和项目
    try:
        user = User.objects.first()
        project = Project.objects.first()
        
        if not user or not project:
            print("❌ 没有找到测试用户或项目")
            return
            
        print(f"✅ 使用用户: {user.username}")
        print(f"✅ 使用项目: {project.PBTitle}")
        
        # 创建AI会话
        session = AITestCaseService.create_generation_session(
            user=user,
            project=project,
            requirement_description="测试对话功能",
            generation_type="functional",
            case_count=0
        )
        
        print(f"✅ 创建会话: {session.session_id}")
        
        # 测试特定消息
        test_message = "请帮我生成测试用例"
        print(f"\n📤 发送消息: {test_message}")
        
        response = AITestCaseService.chat_with_ai(session, test_message)
        print(f"📥 AI响应: {response}")
        
        # 验证响应是否正确
        expected_response = "🎯 请选择需求，基于您的需求，为您生成测试用例"
        if response == expected_response:
            print("✅ 响应正确！")
        else:
            print(f"❌ 响应不正确，期望: {expected_response}")
            
        # 测试其他消息
        other_messages = [
            "生成测试用例",
            "分析用例",
            "补全用例",
            "你好"
        ]
        
        for msg in other_messages:
            print(f"\n📤 发送消息: {msg}")
            response = AITestCaseService.chat_with_ai(session, msg)
            print(f"📥 AI响应: {response[:100]}...")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_ai_chat()
