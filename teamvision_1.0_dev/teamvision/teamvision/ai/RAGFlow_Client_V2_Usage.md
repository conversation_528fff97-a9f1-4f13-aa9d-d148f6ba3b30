# RAGFlow客户端V2使用指南

## 概述

RAGFlow客户端V2支持两种工作模式：
- **Chat模式**：基于知识库的对话式AI，适合复杂的测试用例生成
- **Agent模式**：基于预配置Agent的对话，适合特定场景的快速响应

## 配置要求

在Django settings中配置以下参数：

```python
# RAGFlow基础配置
RAGFLOW_BASE_URL = 'http://localhost:9380'  # RAGFlow服务地址
RAGFLOW_API_KEY = 'your-api-key'            # API密钥

# Chat模式配置
RAGFLOW_DATASET_NAME = 'testcase_generation'  # 数据集名称
RAGFLOW_CHAT_NAME = 'TestCase Assistant'      # 聊天助手名称
RAGFLOW_LLM_MODEL = 'your-llm-model'          # LLM模型名称（可选）

# Agent模式配置
RAGFLOW_AGENT_ID = 'your-agent-id'            # Agent ID
```

## 基本使用

### 1. 创建客户端实例

```python
from teamvision.ai.ragflow_client_v2 import create_ragflow_client

# Chat模式
chat_client = create_ragflow_client(mode="chat")

# Agent模式
agent_client = create_ragflow_client(mode="agent")
```

### 2. 简单对话

```python
# Chat模式对话
response = chat_client.ask("请帮我生成登录功能的测试用例")
print(response)

# Agent模式对话
response = agent_client.ask("请帮我生成测试用例")
print(response)
```

### 3. 生成测试用例

```python
from teamvision.ai.ragflow_client_v2 import TestCaseGenerationRequest

# 创建测试用例生成请求
request = TestCaseGenerationRequest(
    requirement_description="用户登录功能，包括用户名密码验证",
    generation_type="functional",  # functional, boundary, exception, integration
    case_count=5  # 生成数量
)

# 生成测试用例
result = chat_client.generate_test_cases(request)

if result.success:
    print(f"成功生成 {len(result.test_cases)} 个测试用例")
    for case in result.test_cases:
        print(f"- {case.title}")
        print(f"  描述: {case.description}")
        print(f"  步骤: {case.test_steps}")
        print(f"  预期结果: {case.expected_result}")
else:
    print(f"生成失败: {result.message}")
```

## Agent模式高级功能

### 1. 会话管理

```python
# 创建新会话
session_id = agent_client.create_agent_session("测试会话")

# 使用指定会话对话
response = agent_client.ask("生成登录测试用例", session_id=session_id)

# 获取会话对象
session = agent_client.get_agent_session(session_id)
```

### 2. 流式对话

```python
# 启用流式响应
response = agent_client.ask("请详细说明测试流程", stream=True)
```

## Chat模式高级功能

### 1. 知识库管理

```python
# 上传知识文档
success = chat_client.upload_knowledge_document(
    file_path="/path/to/document.pdf",
    display_name="测试规范文档"
)

# 列出知识文档
documents = chat_client.list_knowledge_documents()
for doc in documents:
    print(f"文档: {doc['name']}, 状态: {doc['status']}")

# 删除知识文档
success = chat_client.delete_knowledge_document(document_id)
```

### 2. 测试用例优化

```python
# 优化测试用例
test_case_data = {
    "title": "登录功能测试",
    "description": "测试用户登录",
    # ... 其他字段
}

optimized_case = chat_client.optimize_test_case(
    test_case_data, 
    optimization_type="quality"
)
```

### 3. 测试覆盖度分析

```python
# 分析测试覆盖度
coverage_analysis = chat_client.analyze_test_coverage(
    requirement_text="用户登录功能需求",
    test_cases=[test_case_data1, test_case_data2]
)

print(f"覆盖度: {coverage_analysis['coverage_percentage']}%")
print(f"缺失场景: {coverage_analysis['missing_scenarios']}")
```

## 健康检查

```python
# 检查客户端状态
health = client.health_check()
print(f"状态: {health['status']}")
print(f"模式: {health['mode']}")

if health['status'] == 'healthy':
    print("客户端运行正常")
else:
    print(f"客户端异常: {health['error']}")
```

## 全局实例

系统提供了预定义的全局实例：

```python
from teamvision.ai.ragflow_client_v2 import (
    ragflow_client,        # 默认chat模式
    ragflow_chat_client,   # chat模式
    ragflow_agent_client   # agent模式
)

# 直接使用全局实例
response = ragflow_client.ask("生成测试用例")
```

## 错误处理

```python
try:
    result = client.generate_test_cases(request)
    if not result.success:
        print(f"生成失败: {result.message}")
except Exception as e:
    print(f"客户端错误: {str(e)}")
```

## 最佳实践

1. **模式选择**：
   - 复杂测试用例生成 → Chat模式
   - 快速响应和简单对话 → Agent模式

2. **会话管理**：
   - Agent模式支持多会话，适合并发场景
   - Chat模式每次创建新会话，适合独立任务

3. **错误处理**：
   - 始终检查返回结果的success字段
   - 使用try-catch处理网络和配置错误

4. **性能优化**：
   - 重用客户端实例
   - 合理设置生成数量
   - 定期清理无用会话

## 注意事项

1. 确保RAGFlow服务正常运行
2. Agent模式需要预先配置Agent ID
3. Chat模式首次使用会自动创建数据集和聊天助手
4. 网络异常时客户端会自动重试初始化
