#!/usr/bin/env python
# coding=utf-8
"""
简单测试 ragflow-sdk 是否正常安装和工作
"""

def test_ragflow_sdk_import():
    """测试 ragflow-sdk 导入"""
    print("=" * 60)
    print("测试 ragflow-sdk 导入")
    print("=" * 60)
    
    try:
        from ragflow_sdk import RAGFlow
        print("✓ ragflow-sdk 导入成功")
        print(f"RAGFlow 类: {RAGFlow}")
        return True
    except ImportError as e:
        print(f"❌ ragflow-sdk 导入失败: {str(e)}")
        return False


def test_ragflow_client_creation():
    """测试 RAGFlow 客户端创建"""
    print("\n" + "=" * 60)
    print("测试 RAGFlow 客户端创建")
    print("=" * 60)
    
    try:
        from ragflow_sdk import RAGFlow
        
        # 使用测试配置创建客户端
        client = RAGFlow(
            api_key="test-key",
            base_url="http://localhost:9380"
        )
        
        print("✓ RAGFlow 客户端创建成功")
        print(f"客户端对象: {client}")
        return True
        
    except Exception as e:
        print(f"❌ RAGFlow 客户端创建失败: {str(e)}")
        return False


def test_basic_functionality():
    """测试基本功能（不需要实际连接）"""
    print("\n" + "=" * 60)
    print("测试基本功能")
    print("=" * 60)
    
    try:
        from ragflow_sdk import RAGFlow
        
        client = RAGFlow(
            api_key="test-key",
            base_url="http://localhost:9380"
        )
        
        # 测试方法是否存在
        methods_to_check = [
            'create_dataset',
            'list_datasets',
            'create_chat',
            'list_chats'
        ]
        
        for method_name in methods_to_check:
            if hasattr(client, method_name):
                print(f"✓ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {str(e)}")
        return False


def test_dataclass_creation():
    """测试数据类创建"""
    print("\n" + "=" * 60)
    print("测试数据类创建")
    print("=" * 60)
    
    try:
        # 测试我们自定义的数据类
        import sys
        import os
        
        # 添加当前目录到路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        
        # 模拟 Django settings
        class MockSettings:
            RAGFLOW_BASE_URL = 'http://localhost:9380'
            RAGFLOW_API_KEY = 'test-key'
            RAGFLOW_DATASET_NAME = 'test_dataset'
            RAGFLOW_CHAT_NAME = 'Test Chat'
            RAGFLOW_LLM_MODEL = None
        
        # 模拟 Django 模块
        sys.modules['django'] = type('MockDjango', (), {})()
        sys.modules['django.conf'] = type('MockConf', (), {})()
        sys.modules['django.conf'].settings = MockSettings()
        
        # 模拟其他依赖
        sys.modules['gatesidelib'] = type('MockGatesidelib', (), {})()
        sys.modules['gatesidelib.common'] = type('MockCommon', (), {})()
        sys.modules['gatesidelib.common.simplelogger'] = type('MockLogger', (), {})()
        sys.modules['gatesidelib.common.simplelogger'].SimpleLogger = type('MockSimpleLogger', (), {
            'info': lambda x: None,
            'exception': lambda x: None
        })()
        
        # 现在尝试导入我们的类
        from teamvision.ai.ragflow_client_v2 import TestCaseGenerationRequest, TestCaseItem, TestCaseGenerationResponse
        
        # 创建测试数据
        request = TestCaseGenerationRequest(
            requirement_description="测试需求",
            generation_type="functional",
            case_count=5
        )
        
        test_case = TestCaseItem(
            title="测试用例标题",
            description="测试用例描述",
            precondition="前置条件",
            test_steps=["步骤1", "步骤2"],
            expected_result="预期结果",
            priority=1,
            test_type="functional",
            tags=["tag1", "tag2"]
        )
        
        response = TestCaseGenerationResponse(
            success=True,
            message="成功",
            test_cases=[test_case],
            generation_id="test-id",
            metadata={}
        )
        
        print("✓ TestCaseGenerationRequest 创建成功")
        print(f"  需求描述: {request.requirement_description}")
        print(f"  生成类型: {request.generation_type}")
        print(f"  用例数量: {request.case_count}")
        
        print("✓ TestCaseItem 创建成功")
        print(f"  标题: {test_case.title}")
        print(f"  描述: {test_case.description}")
        
        print("✓ TestCaseGenerationResponse 创建成功")
        print(f"  成功状态: {response.success}")
        print(f"  消息: {response.message}")
        print(f"  测试用例数量: {len(response.test_cases)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据类创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("RAGFlow SDK 测试")
    print("检查 ragflow-sdk 是否正确安装和配置")
    
    results = []
    
    # 运行所有测试
    results.append(test_ragflow_sdk_import())
    results.append(test_ragflow_client_creation())
    results.append(test_basic_functionality())
    results.append(test_dataclass_creation())
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！ragflow-sdk 安装正常")
    else:
        print("❌ 部分测试失败，请检查安装和配置")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
