#!/usr/bin/env python
# coding=utf-8
"""
RAGFlow客户端V2测试脚本
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teamvision.settings')
django.setup()

from teamvision.ai.ragflow_client_v2 import RAGFlowClientV2, TestCaseGenerationRequest


def test_ragflow_client_v2():
    """测试RAGFlow客户端V2"""
    print("=" * 60)
    print("RAGFlow客户端V2测试")
    print("=" * 60)
    
    try:
        # 初始化客户端
        print("\n1. 初始化RAGFlow客户端V2...")
        client = RAGFlowClientV2()
        print("✓ 客户端初始化成功")
        
        # 健康检查
        print("\n2. 执行健康检查...")
        health = client.health_check()
        print(f"健康状态: {health}")
        
        if health['status'] != 'healthy':
            print("❌ 客户端状态不健康，请检查配置")
            return
        
        # 测试测试用例生成
        print("\n3. 测试测试用例生成...")
        request = TestCaseGenerationRequest(
            requirement_description="用户登录功能：用户可以通过用户名和密码登录系统，登录成功后跳转到主页面，登录失败显示错误信息",
            generation_type="functional",
            case_count=5
        )
        
        response = client.generate_test_cases(request)
        
        if response.success:
            print("✓ 测试用例生成成功")
            print(f"生成了 {len(response.test_cases)} 个测试用例:")
            
            for i, test_case in enumerate(response.test_cases, 1):
                print(f"\n测试用例 {i}:")
                print(f"  标题: {test_case.title}")
                print(f"  描述: {test_case.description}")
                print(f"  前置条件: {test_case.precondition}")
                print(f"  测试步骤: {test_case.test_steps}")
                print(f"  预期结果: {test_case.expected_result}")
                print(f"  优先级: {test_case.priority}")
                print(f"  类型: {test_case.test_type}")
                print(f"  标签: {test_case.tags}")
        else:
            print(f"❌ 测试用例生成失败: {response.message}")
        
        # 测试知识文档管理
        print("\n4. 测试知识文档管理...")
        documents = client.list_knowledge_documents()
        print(f"当前知识库中有 {len(documents)} 个文档")
        
        for doc in documents:
            print(f"  - {doc['name']} (状态: {doc['status']}, 块数: {doc['chunk_count']})")
        
        # 测试聊天历史
        print("\n5. 测试聊天历史...")
        history = client.get_chat_history(limit=5)
        print(f"获取到 {len(history)} 条聊天记录")
        
        for record in history[:3]:  # 只显示前3条
            print(f"  - [{record['role']}] {record['content'][:50]}...")
        
        print("\n" + "=" * 60)
        print("✓ RAGFlow客户端V2测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_simple_generation():
    """简单的测试用例生成测试"""
    print("\n" + "=" * 60)
    print("简单测试用例生成测试")
    print("=" * 60)
    
    try:
        client = RAGFlowClientV2()
        
        # 简单的测试请求
        request = TestCaseGenerationRequest(
            requirement_description="请帮我生成测试用例",
            generation_type="functional",
            case_count=3
        )
        
        print("发送请求: 请帮我生成测试用例")
        response = client.generate_test_cases(request)
        
        if response.success:
            print("✓ 生成成功")
            print(f"响应消息: {response.message}")
            print(f"生成的测试用例数量: {len(response.test_cases)}")
        else:
            print(f"❌ 生成失败: {response.message}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")


if __name__ == "__main__":
    # 检查环境变量
    required_settings = [
        'RAGFLOW_BASE_URL',
        'RAGFLOW_API_KEY'
    ]
    
    print("检查环境配置...")
    from django.conf import settings
    
    missing_settings = []
    for setting in required_settings:
        if not hasattr(settings, setting) or not getattr(settings, setting):
            missing_settings.append(setting)
    
    if missing_settings:
        print(f"❌ 缺少必要的配置项: {missing_settings}")
        print("请在settings.py中配置以下项目:")
        for setting in missing_settings:
            print(f"  {setting} = 'your_value_here'")
        sys.exit(1)
    
    print("✓ 环境配置检查通过")
    
    # 运行测试
    test_simple_generation()
    test_ragflow_client_v2()
