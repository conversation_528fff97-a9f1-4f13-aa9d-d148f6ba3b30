# coding=utf-8
"""
AI相关的API视图
"""

import json
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.http import HttpResponse
from django.utils import timezone

from gatesidelib.common.simplelogger import SimpleLogger
from teamvision.api.ai.filter import AIUsageStatisticsFilter
from teamvision.api.project.views.CsrfExemptSessionAuthentication import CsrfExemptSessionAuthentication
from teamvision.project.models import Project, Requirement
from teamvision.ai.models import (
    AIConfiguration, AIGenerationSession, AIGeneratedTestCase, 
    AIChatHistory, AIUsageStatistics
)
from teamvision.api.ai.serializers import (
    AIConfigurationSerializer, AIGenerationSessionSerializer, AIGenerationSessionCreateSerializer,
    AIGeneratedTestCaseSerializer, AIGeneratedTestCaseUpdateSerializer,
    AIChatHistorySerializer, AIUsageStatisticsSerializer,
    TestCaseGenerationRequestSerializer, TestCaseAcceptanceSerializer,
    TestCaseOptimizationSerializer, AIChatRequestSerializer,
    TestCoverageAnalysisSerializer
)
from teamvision.ai.services import AITestCaseService, AIAnalysisService
from rest_framework.permissions import AllowAny


class AIGenerationSessionListCreateView(generics.ListCreateAPIView):
    """AI生成会话列表和创建"""
    
    serializer_class = AIGenerationSessionSerializer
    authentication_classes = [CsrfExemptSessionAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return AIGenerationSessionCreateSerializer
    
    def get_queryset(self):
        queryset = AIGenerationSession.objects.filter(user=self.request.user)
        
        # 过滤参数
        project_id = self.request.query_params.get('project_id')
        status_filter = self.request.query_params.get('status')
        generation_type = self.request.query_params.get('generation_type')
        
        if project_id:
            queryset = queryset.filter(project=project_id)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        if generation_type:
            queryset = queryset.filter(generation_type=generation_type)
        
        return queryset.order_by('-created_time')
  
    def post(self, request, *args, **kwargs):
        session = AITestCaseService.create_generation_session(request.data , user=self.request.user)
        serializer = AIGenerationSessionCreateSerializer(instance=session)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class AIGenerationSessionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """AI生成会话详情"""
    
    serializer_class = AIGenerationSessionSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return AIGenerationSession.objects.filter(user=self.request.user)


class AIGeneratedTestCaseListView(generics.ListAPIView):
    """AI生成的测试用例列表"""
    
    serializer_class = AIGeneratedTestCaseSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        session_id = self.kwargs.get('session_id')
        session = get_object_or_404(
            AIGenerationSession, 
            session_id=session_id, 
            user=self.request.user
        )
        
        queryset = AIGeneratedTestCase.objects.filter(session=session)
        
        # 过滤参数
        status_filter = self.request.query_params.get('status')
        test_type = self.request.query_params.get('test_type')
        priority = self.request.query_params.get('priority')
        
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        if test_type:
            queryset = queryset.filter(test_type=test_type)
        if priority:
            queryset = queryset.filter(priority=priority)
        
        return queryset.order_by('-created_time')


class AIGeneratedTestCaseDetailView(generics.RetrieveUpdateDestroyAPIView):
    """AI生成测试用例详情"""
    
    serializer_class = AIGeneratedTestCaseUpdateSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return AIGeneratedTestCase.objects.filter(session__user=self.request.user)


class TestCaseGenerationView(APIView):
    """
      测试用例生成API
    """

    authentication_classes = [CsrfExemptSessionAuthentication]
    permission_classes = [AllowAny]
    
    def post(self, request):
        serializer = TestCaseGenerationRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {'error': '参数验证失败', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # 判断session_id是否存在
            if 'session_id' in request.data:
                session = get_object_or_404(
                    AIGenerationSession,
                    session_id=request.data['session_id'],
                    user=request.user
                )
            else:
                session = AITestCaseService.create_generation_session(request.data, user=request.user)
            
            # 相关需求
            requirement_id = request.data.get('requirement_id')
            if requirement_id:
                requirement = get_object_or_404(Requirement, id=requirement_id)
                session.requirement_description = requirement.Title
                session.save()
            
            # 生成测试用例
            response = AITestCaseService.generate_test_cases(session)
            
            if response.success:
                return Response({
                    'success': True,
                    'message': '测试用例生成成功',
                    'session_id': session.session_id,
                    'generated_count': len(response.test_cases),
                    'test_cases': [
                        {
                            'title': case.title,
                            'description': case.description,
                            'precondition': case.precondition,
                            'test_steps': case.test_steps,
                            'expected_result': case.expected_result,
                            'priority': case.priority,
                            'test_type': case.test_type,
                            'tags': case.tags
                        }
                        for case in response.test_cases
                    ]
                })
            else:
                return Response({
                    'success': False,
                    'message': response.message,
                    'session_id': session.session_id
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Exception as e:
            SimpleLogger.exception(f"测试用例生成API异常: {str(e)}")
            return Response({
                'success': False,
                'message': f'生成失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseAcceptanceView(APIView):
    """测试用例采纳API"""
    
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        serializer = TestCaseAcceptanceSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {'error': '参数验证失败', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            accepted_cases = AITestCaseService.batch_accept_test_cases(
                ai_case_ids=serializer.validated_data['ai_case_ids'],
                parent_id=serializer.validated_data['parent_id'],
                module_id=serializer.validated_data['module_id']
            )
            
            return Response({
                'success': True,
                'message': f'成功采纳{len(accepted_cases)}个测试用例',
                'accepted_count': len(accepted_cases),
                'project_case_ids': [case.id for case in accepted_cases]
            })
            
        except Exception as e:
            SimpleLogger.exception(f"测试用例采纳API异常: {str(e)}")
            return Response({
                'success': False,
                'message': f'采纳失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseOptimizationView(APIView):
    """测试用例优化API"""
    
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        serializer = TestCaseOptimizationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {'error': '参数验证失败', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            ai_case = get_object_or_404(
                AIGeneratedTestCase, 
                id=serializer.validated_data['ai_case_id'],
                session__user=request.user
            )
            
            optimized_data = AITestCaseService.optimize_test_case(
                ai_case=ai_case,
                optimization_type=serializer.validated_data['optimization_type']
            )
            
            if 'error' in optimized_data:
                return Response({
                    'success': False,
                    'message': optimized_data['error']
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            return Response({
                'success': True,
                'message': '测试用例优化完成',
                'optimized_case': optimized_data
            })
            
        except Exception as e:
            SimpleLogger.exception(f"测试用例优化API异常: {str(e)}")
            return Response({
                'success': False,
                'message': f'优化失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AIChatView(APIView):
    """AI对话API"""

    authentication_classes = [CsrfExemptSessionAuthentication]
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        serializer = AIChatRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {'error': '参数验证失败', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            session = get_object_or_404(
                AIGenerationSession,
                session_id=serializer.validated_data['session_id'],
                user=request.user
            )
            
            ai_response = AITestCaseService.chat_with_ai(
                session=session,
                user_message=serializer.validated_data['message']
            )

            return Response({
                'success': True,
                'response': ai_response,
                'session_id': session.session_id
            })
            
        except Exception as e:
            SimpleLogger.exception(f"AI对话API异常: {str(e)}")
            return Response({
                'success': False,
                'message': f'对话失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AIChatHistoryView(generics.ListAPIView):
    """AI对话历史"""
    
    serializer_class = AIChatHistorySerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        session_id = self.kwargs.get('session_id')
        session = get_object_or_404(AIGenerationSession, session_id=session_id, user=self.request.user)
        
        return AIChatHistory.objects.filter(session=session).order_by('created_time')


class TestCoverageAnalysisView(APIView):
    """测试覆盖度分析API"""
    
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        serializer = TestCoverageAnalysisSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {'error': '参数验证失败', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            project = get_object_or_404(Project, id=serializer.validated_data['project_id'])
            
            analysis_result = AIAnalysisService.analyze_test_coverage(
                project=project,
                requirement_text=serializer.validated_data['requirement_text']
            )
            
            if 'error' in analysis_result:
                return Response({
                    'success': False,
                    'message': analysis_result['error']
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            return Response({
                'success': True,
                'analysis': analysis_result
            })
            
        except Exception as e:
            SimpleLogger.exception(f"测试覆盖度分析API异常: {str(e)}")
            return Response({
                'success': False,
                'message': f'分析失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AIUsageStatisticsView(generics.ListAPIView):
    """AI使用统计"""

    serializer_class = AIUsageStatisticsSerializer
    permission_classes = [IsAuthenticated]
    filterset_class = AIUsageStatisticsFilter

    def get_queryset(self):
        queryset = AIUsageStatistics.objects.filter(user=self.request.user)

        # 过滤参数
        project = self.request.query_params.get('project')
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')

        if project:
            queryset = queryset.filter(project=project)
        if date_from:
            queryset = queryset.filter(date__gte=date_from)
        if date_to:
            queryset = queryset.filter(date__lte=date_to)

        return queryset.order_by('-date')


class AIServiceStatusView(APIView):
    """AI服务状态检查"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            from teamvision.ai.ragflow_client import ragflow_client

            # 简单的连接测试
            # 这里可以添加实际的RAGFlow健康检查
            status = 'available'
            message = 'AI服务正常'

            return Response({
                'status': status,
                'message': message,
                'timestamp': timezone.now().isoformat()
            })

        except Exception as e:
            SimpleLogger.exception(f"AI服务状态检查失败: {str(e)}")
            return Response({
                'status': 'unavailable',
                'message': f'AI服务不可用: {str(e)}',
                'timestamp': timezone.now().isoformat()
            })

class AIServiceStatusV2View(APIView):
    """AI服务状态检查 V2版本"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            from teamvision.ai.ragflow_client_v2 import ragflow_client_v2

            # 执行健康检查
            health_status = ragflow_client_v2.health_check()
            
            return Response({
                'status': health_status['status'],
                'message': 'AI服务状态检查完成（V2版本）',
                'version': 'v2',
                'health_details': health_status,
                'timestamp': timezone.now().isoformat()
            })

        except Exception as e:
            SimpleLogger.exception(f"AI服务状态检查失败（V2）: {str(e)}")
            return Response({
                'status': 'unavailable',
                'message': f'AI服务不可用（V2）: {str(e)}',
                'version': 'v2',
                'timestamp': timezone.now().isoformat()
            })

class AIModelInfoView(APIView):
    """AI模型信息"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        return Response({
            'model_name': 'RAGFlow Agent',
            'version': '1.0.0',
            'capabilities': [
                'test_case_generation',
                'test_case_optimization',
                'coverage_analysis',
                'natural_language_chat'
            ],
            'supported_languages': ['zh-CN', 'en-US'],
            'max_cases_per_request': 50,
            'max_requests_per_day': 100
        })


class BatchUpdateAICasesStatusView(APIView):
    """批量更新AI生成用例状态"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        case_ids = request.data.get('case_ids', [])
        status = request.data.get('status', 'reviewed')

        if not case_ids:
            return Response({
                'success': False,
                'message': '请提供要更新的用例ID'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            updated_count = AIGeneratedTestCase.objects.filter(
                id__in=case_ids,
                session__user=request.user
            ).update(status=status)

            return Response({
                'success': True,
                'message': f'成功更新{updated_count}个测试用例状态',
                'updated_count': updated_count
            })

        except Exception as e:
            SimpleLogger.exception(f"批量更新用例状态失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'更新失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BatchDeleteAICasesView(APIView):
    """批量删除AI生成用例"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        case_ids = request.data.get('case_ids', [])

        if not case_ids:
            return Response({
                'success': False,
                'message': '请提供要删除的用例ID'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            deleted_count, _ = AIGeneratedTestCase.objects.filter(
                id__in=case_ids,
                session__user=request.user
            ).delete()

            return Response({
                'success': True,
                'message': f'成功删除{deleted_count}个测试用例',
                'deleted_count': deleted_count
            })

        except Exception as e:
            SimpleLogger.exception(f"批量删除用例失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'删除失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BatchRateAICasesView(APIView):
    """批量评分AI生成用例"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        ratings = request.data.get('ratings', [])

        if not ratings:
            return Response({
                'success': False,
                'message': '请提供评分数据'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            updated_count = 0

            for rating_data in ratings:
                case_id = rating_data.get('case_id')
                rating = rating_data.get('rating')
                feedback = rating_data.get('feedback', '')

                if case_id and rating:
                    AIGeneratedTestCase.objects.filter(
                        id=case_id,
                        session__user=request.user
                    ).update(
                        user_rating=rating,
                        user_feedback=feedback
                    )
                    updated_count += 1

            return Response({
                'success': True,
                'message': f'成功评分{updated_count}个测试用例',
                'updated_count': updated_count
            })

        except Exception as e:
            SimpleLogger.exception(f"批量评分用例失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'评分失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ExportAIGeneratedCasesView(APIView):
    """导出AI生成的测试用例"""

    permission_classes = [IsAuthenticated]

    def get(self, request, session_id):
        format_type = request.query_params.get('format', 'excel')

        try:
            session = get_object_or_404(
                AIGenerationSession,
                session_id=session_id,
                user=request.user
            )

            ai_cases = AIGeneratedTestCase.objects.filter(session=session)

            if format_type == 'excel':
                return self.export_to_excel(ai_cases, session)
            elif format_type == 'json':
                return self.export_to_json(ai_cases, session)
            else:
                return Response({
                    'success': False,
                    'message': '不支持的导出格式'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            SimpleLogger.exception(f"导出AI生成用例失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'导出失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def export_to_excel(self, ai_cases, session):
        """导出为Excel格式"""
        import io
        import xlwt

        # 创建工作簿
        workbook = xlwt.Workbook(encoding='utf-8')
        worksheet = workbook.add_sheet('AI生成测试用例')

        # 设置列标题
        headers = ['序号', '标题', '描述', '前置条件', '测试步骤', '预期结果', '优先级', '测试类型', '标签', '状态']
        for col, header in enumerate(headers):
            worksheet.write(0, col, header)

        # 写入数据
        for row, case in enumerate(ai_cases, 1):
            worksheet.write(row, 0, row)
            worksheet.write(row, 1, case.title)
            worksheet.write(row, 2, case.description)
            worksheet.write(row, 3, case.precondition)
            worksheet.write(row, 4, '\n'.join(case.test_steps) if case.test_steps else '')
            worksheet.write(row, 5, case.expected_result)
            worksheet.write(row, 6, case.priority)
            worksheet.write(row, 7, case.test_type)
            worksheet.write(row, 8, ', '.join(case.tags) if case.tags else '')
            worksheet.write(row, 9, case.get_status_display())

        # 保存到内存
        output = io.BytesIO()
        workbook.save(output)
        output.seek(0)

        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.ms-excel'
        )
        response['Content-Disposition'] = f'attachment; filename="AI生成测试用例_{session.module_name}_{session.created_time.strftime("%Y%m%d")}.xls"'

        return response

    def export_to_json(self, ai_cases, session):
        """导出为JSON格式"""
        data = {
            'session_info': {
                'session_id': session.session_id,
                'module_name': session.module_name,
                'requirement_description': session.requirement_description,
                'generation_type': session.generation_type,
                'created_time': session.created_time.isoformat()
            },
            'test_cases': []
        }

        for case in ai_cases:
            data['test_cases'].append({
                'id': case.id,
                'title': case.title,
                'description': case.description,
                'precondition': case.precondition,
                'test_steps': case.test_steps,
                'expected_result': case.expected_result,
                'priority': case.priority,
                'test_type': case.test_type,
                'tags': case.tags,
                'status': case.status,
                'created_time': case.created_time.isoformat()
            })

        response = HttpResponse(
            json.dumps(data, ensure_ascii=False, indent=2),
            content_type='application/json'
        )
        response['Content-Disposition'] = f'attachment; filename="AI生成测试用例_{session.module_name}_{session.created_time.strftime("%Y%m%d")}.json"'

        return response


class ExportChatHistoryView(APIView):
    """导出AI对话历史"""

    permission_classes = [IsAuthenticated]

    def get(self, request, session_id):
        format_type = request.query_params.get('format', 'txt')

        try:
            session = get_object_or_404(
                AIGenerationSession,
                session_id=session_id,
                user=request.user
            )

            chat_history = AIChatHistory.objects.filter(session=session).order_by('created_time')

            if format_type == 'txt':
                return self.export_to_txt(chat_history, session)
            elif format_type == 'json':
                return self.export_to_json_history(chat_history, session)
            else:
                return Response({
                    'success': False,
                    'message': '不支持的导出格式'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            SimpleLogger.exception(f"导出对话历史失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'导出失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def export_to_txt(self, chat_history, session):
        """导出为文本格式"""
        content = f"AI对话历史 - {session.module_name}\n"
        content += f"会话ID: {session.session_id}\n"
        content += f"创建时间: {session.created_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        content += "=" * 50 + "\n\n"

        for msg in chat_history:
            sender = "用户" if msg.message_type == 'user' else "AI助手"
            content += f"[{msg.created_time.strftime('%H:%M:%S')}] {sender}:\n"
            content += f"{msg.content}\n\n"

        response = HttpResponse(content, content_type='text/plain; charset=utf-8')
        response['Content-Disposition'] = f'attachment; filename="AI对话历史_{session.module_name}_{session.created_time.strftime("%Y%m%d")}.txt"'

        return response

    def export_to_json_history(self, chat_history, session):
        """导出为JSON格式"""
        data = {
            'session_info': {
                'session_id': session.session_id,
                'module_name': session.module_name,
                'created_time': session.created_time.isoformat()
            },
            'chat_history': []
        }

        for msg in chat_history:
            data['chat_history'].append({
                'message_type': msg.message_type,
                'content': msg.content,
                'created_time': msg.created_time.isoformat(),
                'metadata': msg.metadata
            })

        response = HttpResponse(
            json.dumps(data, ensure_ascii=False, indent=2),
            content_type='application/json'
        )
        response['Content-Disposition'] = f'attachment; filename="AI对话历史_{session.module_name}_{session.created_time.strftime("%Y%m%d")}.json"'

        return response


class AIHealthCheckView(APIView):
    """AI服务健康检查"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        from teamvision.ai.health_check import get_ai_health_status, perform_ai_health_check

        # 检查是否需要强制刷新
        force_check = request.query_params.get('force', 'false').lower() == 'true'

        if force_check:
            status = perform_ai_health_check()
        else:
            status = get_ai_health_status()

        return Response(status)


class AIUsageMetricsView(APIView):
    """AI使用指标"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        from teamvision.ai.health_check import get_ai_usage_metrics

        days = int(request.query_params.get('days', 7))
        metrics = get_ai_usage_metrics(days)

        return Response(metrics)


class AIConfigurationView(generics.ListCreateAPIView):
    """AI配置管理"""

    serializer_class = AIConfigurationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = AIConfiguration.objects.filter(is_active=True)

        # 过滤参数
        config_type = self.request.query_params.get('config_type')
        project_id = self.request.query_params.get('project_id')

        if config_type:
            queryset = queryset.filter(config_type=config_type)

        if project_id:
            queryset = queryset.filter(Q(project=project_id) | Q(project__isnull=True)).order_by('-project')  # 项目特定配置优先

        return queryset

    def perform_create(self, serializer):
        # 如果指定了项目ID，关联到项目
        project_id = self.request.data.get('project_id')
        if project_id:
            from teamvision.project.models import Project
            project = get_object_or_404(Project, id=project_id)
            serializer.save(user=self.request.user, project=project)
        else:
            serializer.save(user=self.request.user)


class AIConfigurationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """AI配置详情"""

    serializer_class = AIConfigurationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return AIConfiguration.objects.filter(Q(user=self.request.user) | Q(user__isnull=True))


class KnowledgeManagementView(APIView):
    """知识库管理"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """列出知识库文档"""
        try:
            from teamvision.ai.ragflow_client_v2 import ragflow_client_v2
            
            documents = ragflow_client_v2.list_knowledge_documents()
            
            return Response({
                'success': True,
                'documents': documents,
                'count': len(documents),
                'version': 'v2'
            })
            
        except Exception as e:
            SimpleLogger.exception(f"列出知识库文档失败（V2）: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取文档列表失败: {str(e)}',
                'version': 'v2'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """上传知识库文档"""
        try:
            from teamvision.ai.ragflow_client_v2 import ragflow_client_v2
            
            file_obj = request.FILES.get('file')
            if not file_obj:
                return Response({
                    'success': False,
                    'message': '请提供要上传的文件',
                    'version': 'v2'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 保存临时文件
            import tempfile
            import os
            
            with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file_obj.name}") as tmp_file:
                for chunk in file_obj.chunks():
                    tmp_file.write(chunk)
                tmp_file_path = tmp_file.name
            
            try:
                # 上传到知识库
                success = ragflow_client_v2.upload_knowledge_document(
                    file_path=tmp_file_path,
                    display_name=file_obj.name
                )
                
                if success:
                    return Response({
                        'success': True,
                        'message': f'文档 {file_obj.name} 上传成功',
                        'filename': file_obj.name,
                        'version': 'v2'
                    })
                else:
                    return Response({
                        'success': False,
                        'message': f'文档 {file_obj.name} 上传失败',
                        'version': 'v2'
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    
            finally:
                # 清理临时文件
                if os.path.exists(tmp_file_path):
                    os.unlink(tmp_file_path)
            
        except Exception as e:
            SimpleLogger.exception(f"上传知识库文档失败（V2）: {str(e)}")
            return Response({
                'success': False,
                'message': f'上传失败: {str(e)}',
                'version': 'v2'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request):
        """删除知识库文档"""
        try:
            from teamvision.ai.ragflow_client_v2 import ragflow_client_v2
            
            document_id = request.data.get('document_id')
            if not document_id:
                return Response({
                    'success': False,
                    'message': '请提供要删除的文档ID',
                    'version': 'v2'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            success = ragflow_client_v2.delete_knowledge_document(document_id)
            
            if success:
                return Response({
                    'success': True,
                    'message': f'文档 {document_id} 删除成功',
                    'version': 'v2'
                })
            else:
                return Response({
                    'success': False,
                    'message': f'文档 {document_id} 删除失败',
                    'version': 'v2'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Exception as e:
            SimpleLogger.exception(f"删除知识库文档失败（V2）: {str(e)}")
            return Response({
                'success': False,
                'message': f'删除失败: {str(e)}',
                'version': 'v2'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        


@api_view(['POST'])
@permission_classes([AllowAny])
def simple_chat_v2(request):
    """简单的AI对话接口 V2版本"""
    
    try:
        message = request.data.get('message', '请帮我生成测试用例')
        
        # 使用V2服务生成响应
        if message.strip() == '请帮我生成测试用例':
            response = "🎯 请选择需求，基于您的需求，为您生成测试用例"
        else:
            response = f"收到您的消息：{message}。我是AI测试助手（V2版本），可以帮您生成测试用例、分析用例质量、补全用例内容等。请告诉我您需要什么帮助？"
        
        return Response({
            'success': True,
            'response': response,
            'version': 'v2',
            'timestamp': timezone.now().isoformat()
        })
        
    except Exception as e:
        SimpleLogger.exception(f"简单AI对话失败（V2）: {str(e)}")
        return Response({
            'success': False,
            'message': f'对话失败: {str(e)}',
            'version': 'v2'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    